import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Badge,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  DarkMode,
  LightMode,
} from "@mui/icons-material";
import { useState } from "react";

function Header() {
  const [darkMode, setDarkMode] = useState(false);
  const handleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Toolbar
        sx={{
          justifyContent: "space-between",
          minHeight: "64px",
          bgcolor: darkMode ? "#2a2a2a" : "#fff",
          borderBottom: 1,
          borderColor: "#ccc",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            color: darkMode ? "#fff" : "#000",
          }}
        >
          <Tab label="Text" sx={{ opacity: "unset" }} />
          <Tab label="Text" sx={{ opacity: "unset" }} />
          <Tab label="Text" sx={{ opacity: "unset" }} />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
          }}
        >
          <TextField
            size="small"
            placeholder="Search"
            variant="outlined"
            sx={{
              width: 200,
              borderRadius: 3,
              bgcolor: darkMode ? "#3f3f3f" : "#f5f5f5",
              caretColor: darkMode ? "#fff" : "#000",

              "& input": {
                color: darkMode ? "#fff" : "#000",
              },
              "input::placeholder": {
                color: darkMode ? "#fff" : "#000",
                opacity: "unset",
              },
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  border: "none",
                },
                "&:hover fieldset": {
                  border: "none",
                },
                "&.Mui-focused fieldset": {
                  border: "none",
                },
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon
                    fontSize="small"
                    sx={{ color: darkMode ? "#fff" : "#000" }}
                  />
                </InputAdornment>
              ),
            }}
          />

          <Typography variant="body2" sx={{ color: darkMode ? "#fff" : "000" }}>
            Text Text Text
          </Typography>

          <IconButton
            sx={{
              p: 0,
              color: darkMode ? "#fff" : "#000",
              "& svg": {
                width: "20px",
                height: "20px",
              },
            }}
            color="inherit"
            onClick={handleDarkMode}
          >
            {darkMode ? <LightMode /> : <DarkMode />}
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

export default Header;
